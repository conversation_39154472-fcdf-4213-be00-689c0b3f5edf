name: msct-service

services:
#  kafka:
#    image: apache/kafka:3.7.0
#    hostname: kafka
#    container_name: kafka
#    env_file: environments/.env.kafka
#    healthcheck:
#      test: bash -c "if [[ ! -f ~/kafka/kafka.ready ]] then exit -1; fi"
#      interval: 5s
#      timeout: 10s
#      retries: 50
#    ports:
#      - "9092:9092"
#    entrypoint: /kafka/start-kafka.sh
#    volumes:
#      - ./kafka/:/kafka/
#    networks:
#      - msct_network
  zookeeper:
    image: zookeeper:3.8.0
    hostname: zookeeper
    container_name: zookeeper
    networks:
      - msct_network
    ports:
      - 2181:2181

  # Kafka node
  kafka:
    image: wurstmeister/kafka:2.13-2.8.1
    hostname: kafka
    container_name: kafka
    networks:
      - msct_network
    ports:
      - "9092:9092"
      - "9094:9094"
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_CREATE_TOPICS: >-
        old.market.stream:3:1
      KAFKA_ADVERTISED_HOST_NAME: ${DOCKER_HOST_IP:-127.0.0.1}
#      KAFKA_LISTENERS: PLAINTEXT://:9092
      KAFKA_LISTENERS: "PLAINTEXT://kafka:29092,CONTROLLER://kafka:39092,PLAINTEXT_HOST://0.0.0.0:9092"
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: "PLAINTEXT://kafka:29092,PLAINTEXT_HOST://${DOCKER_HOST_IP:-127.0.0.1}:9092"
      KAFKA_CONTROLLER_QUORUM_VOTERS: "1@$kafka:9092"
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_CONTROLLER_LISTENER_NAMES: CONTROLLER

    depends_on:
      - zookeeper

  kafka-ui:
    image: ghcr.io/kafbat/kafka-ui:v1.1.0
    container_name: kafka-ui
    env_file: environments/.env.kafka
    ports:
      - "8085:8080"
    environment:
      - SPRING_CONFIG_ADDITIONAL-LOCATION=/tmp/config.yml
    volumes:
      - ./kafka-ui/config.yml:/tmp/config.yml:ro
    depends_on:
      - kafka
    networks:
      - msct_network

networks:
  msct_network:
