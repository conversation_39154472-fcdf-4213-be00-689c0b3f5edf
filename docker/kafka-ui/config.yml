kafka:
  clusters:
    - name: local
      bootstrapServers: kafka:29092
      zookeeper: zookeeper:2181
    - name: fd-cluster
      bootstrapServers: fokfd-nxtbs1-use1.dev.fndlsb.net:9092
#      bootstrapServers: *************:9092
#      zookeeper: use1-fozfd01-nxtbs1.dev.fndlsb.net:2181
      readonly: true
#      serde:
#        - name: market-stream
#          className: com.betfair.platform.fms.serializers.protobuffer.MarketChangeSerDeser



#      serde:
#        - name: FipInstruction
#          className: io.kafbat.ui.serdes.builtin.ProtobufFileSerde
#          properties:
#            protobufFilesDir: /tmp/fipprotofiles
#            protobufMessageName: com.ppb.feeds.model.Instruction

