#!/bin/bash -e

KAFKA_HOME=/opt/kafka

if [[ -z "$KAFKA_CREATE_TOPICS" ]]; then
    exit 0
fi

if [[ -z "$START_TIMEOUT" ]]; then
    START_TIMEOUT=600
fi

start_timeout_exceeded=false
count=0
step=10

while ! exec 6<>/dev/tcp/127.0.0.1/$KAFKA_CONTAINER_PORT 2>&1 > /dev/null; do
    echo "waiting for kafka to be ready"
    sleep $step;
    count=$((count + step))
    if [ $count -gt $START_TIMEOUT ]; then
        start_timeout_exceeded=true
        break
    fi
done

if $start_timeout_exceeded; then
    echo "Not able to auto-create topic (waited for $START_TIMEOUT sec)"
    exit 1
fi

#remove spaces to allow declaring the variable in a multiline manner
KAFKA_CREATE_TOPICS=$(echo $KAFKA_CREATE_TOPICS | tr -d ' ')

numberOfTopics=0

# Expected format:
#   name:partitions:replicas:cleanup.policy
IFS="${KAFKA_CREATE_TOPICS_SEPARATOR-,}"; for topicToCreate in $KAFKA_CREATE_TOPICS; do
    echo "creating topics: $topicToCreate"
    IFS=':' read -r -a topicConfig <<< "$topicToCreate"
    config=
    if [ -n "${topicConfig[3]}" ]; then
        config="--config=cleanup.policy=${topicConfig[3]}"
    fi

    COMMAND="JMX_PORT='' ${KAFKA_HOME}/bin/kafka-topics.sh \\
		--create \\
		--bootstrap-server ${KAFKA_CONTAINER_HOST}:${KAFKA_CONTAINER_PORT} \\
		--topic ${topicConfig[0]} \\
		--partitions ${topicConfig[1]} \\
		--replication-factor ${topicConfig[2]} \\
		${config} \\
		${KAFKA_0_10_OPTS} &"

    eval "${COMMAND}"

    numberOfTopics=`expr $numberOfTopics + 1`
done

# Make sure that all topics are created for health check
topicsListCommand="${KAFKA_HOME}/bin/kafka-topics.sh --bootstrap-server=localhost:9092 --list | wc -l"

NUMBER_TOPICS_CREATED=`eval $topicsListCommand`

while [[ $NUMBER_TOPICS_CREATED -lt $numberOfTopics ]]
do
  NUMBER_TOPICS_CREATED=`eval $topicsListCommand`
done

mkdir -p ~/kafka/
touch ~/kafka/kafka.ready

wait
