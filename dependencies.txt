[INFO] Scanning for projects...
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.pom
Progress (1): 1.4/4.7 kB
Progress (1): 2.8/4.7 kB
Progress (1): 4.1/4.7 kB
Progress (1): 4.7 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.pom (4.7 kB at 13 kB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/apache/johnzon/johnzon/1.2.21/johnzon-1.2.21.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/apache/johnzon/johnzon/1.2.21/johnzon-1.2.21.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/apache/johnzon/johnzon/1.2.21/johnzon-1.2.21.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/apache/johnzon/johnzon/1.2.21/johnzon-1.2.21.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/apache/johnzon/johnzon/1.2.21/johnzon-1.2.21.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon/1.2.21/johnzon-1.2.21.pom
Progress (1): 1.4/31 kB
Progress (1): 2.8/31 kB
Progress (1): 4.1/31 kB
Progress (1): 5.5/31 kB
Progress (1): 6.9/31 kB
Progress (1): 8.3/31 kB
Progress (1): 9.7/31 kB
Progress (1): 11/31 kB 
Progress (1): 12/31 kB
Progress (1): 14/31 kB
Progress (1): 15/31 kB
Progress (1): 16/31 kB
Progress (1): 18/31 kB
Progress (1): 19/31 kB
Progress (1): 21/31 kB
Progress (1): 22/31 kB
Progress (1): 23/31 kB
Progress (1): 25/31 kB
Progress (1): 26/31 kB
Progress (1): 27/31 kB
Progress (1): 29/31 kB
Progress (1): 30/31 kB
Progress (1): 31 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon/1.2.21/johnzon-1.2.21.pom (31 kB at 1.5 MB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.jar
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.jar
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.jar
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.jar
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.jar
Progress (1): 16/35 kB
Progress (1): 33/35 kB
Progress (1): 35 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon-maven-plugin/1.2.21/johnzon-maven-plugin-1.2.21.jar (35 kB at 1.9 MB/s)
[INFO] 
[INFO] -------------< com.flutter:market-stream-comparison-tool >--------------
[INFO] Building market-stream-comparison-tool 0.0.1-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[WARNING] 2 problems were encountered while building the effective model for org.apache.yetus:audience-annotations:jar:0.5.0 during dependency collection step for project (use -X to see details)
[INFO] 
[INFO] --- dependency:3.3.0:tree (default-cli) @ market-stream-comparison-tool ---
[INFO] com.flutter:market-stream-comparison-tool:jar:0.0.1-SNAPSHOT
[INFO] +- com.ppb.platform.sb:market-stream-client:jar:4.17.0:compile
[INFO] |  +- com.ppb.platform.stream:stream-protocol-consumer:jar:3.1.3_2.1_2.11:compile
[INFO] |  |  +- com.ppb.platform.stream:stream-protocol-metadata:jar:3.1.3_2.1_2.11:compile
[INFO] |  |  |  \- org.apache.curator:curator-recipes:jar:4.0.1:compile
[INFO] |  |  |     \- org.apache.curator:curator-framework:jar:4.0.1:compile
[INFO] |  |  |        \- org.apache.curator:curator-client:jar:4.0.1:compile
[INFO] |  |  +- org.scala-lang:scala-library:jar:2.11.12:compile
[INFO] |  |  +- org.scala-lang:scala-reflect:jar:2.11.12:compile
[INFO] |  |  +- org.apache.kafka:kafka_2.11:jar:2.1.1:compile
[INFO] |  |  |  +- org.apache.kafka:kafka-clients:jar:3.1.2:compile
[INFO] |  |  |  |  +- com.github.luben:zstd-jni:jar:1.5.0-4:runtime
[INFO] |  |  |  |  +- org.lz4:lz4-java:jar:1.8.0:runtime
[INFO] |  |  |  |  \- org.xerial.snappy:snappy-java:jar:*******:runtime
[INFO] |  |  |  +- net.sf.jopt-simple:jopt-simple:jar:5.0.4:compile
[INFO] |  |  |  +- com.yammer.metrics:metrics-core:jar:2.2.0:compile
[INFO] |  |  |  +- com.typesafe.scala-logging:scala-logging_2.11:jar:3.9.0:compile
[INFO] |  |  |  \- com.101tec:zkclient:jar:0.11:compile
[INFO] |  |  +- com.typesafe.akka:akka-actor_2.11:jar:2.5.25:compile
[INFO] |  |  +- com.typesafe.akka:akka-slf4j_2.11:jar:2.5.25:compile
[INFO] |  |  +- org.apache.zookeeper:zookeeper:jar:3.4.13:compile
[INFO] |  |  |  +- jline:jline:jar:0.9.94:compile
[INFO] |  |  |  +- org.apache.yetus:audience-annotations:jar:0.5.0:compile
[INFO] |  |  |  \- io.netty:netty:jar:3.10.6.Final:compile
[INFO] |  |  +- net.cakesolutions:scala-kafka-client_2.11:jar:2.1.0:compile
[INFO] |  |  +- com.typesafe.akka:akka-stream_2.11:jar:2.5.25:compile
[INFO] |  |  |  +- com.typesafe.akka:akka-protobuf_2.11:jar:2.5.25:compile
[INFO] |  |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  |  \- com.typesafe:ssl-config-core_2.11:jar:0.3.8:compile
[INFO] |  |  |     \- org.scala-lang.modules:scala-parser-combinators_2.11:jar:1.1.1:compile
[INFO] |  |  +- net.cakesolutions:scala-kafka-client-akka_2.11:jar:2.1.0:compile
[INFO] |  |  +- com.iheart:ficus_2.11:jar:1.4.7:compile
[INFO] |  |  |  \- org.typelevel:macro-compat_2.11:jar:1.1.1:compile
[INFO] |  |  +- com.typesafe.akka:akka-http_2.11:jar:10.1.7:compile
[INFO] |  |  +- com.typesafe.akka:akka-http-core_2.11:jar:10.1.7:compile
[INFO] |  |  |  \- com.typesafe.akka:akka-parsing_2.11:jar:10.1.7:compile
[INFO] |  |  +- com.typesafe.akka:akka-http-spray-json_2.11:jar:10.1.7:compile
[INFO] |  |  |  \- io.spray:spray-json_2.11:jar:1.3.5:compile
[INFO] |  |  +- org.aspectj:aspectjrt:jar:1.9.7:compile
[INFO] |  |  +- com.google.code.gson:gson:jar:2.9.1:compile
[INFO] |  |  +- com.typesafe.akka:akka-stream-kafka_2.11:jar:1.0.4:compile
[INFO] |  |  +- com.typesafe.akka:akka-remote_2.11:jar:2.5.25:compile
[INFO] |  |  |  +- io.aeron:aeron-driver:jar:1.15.1:compile
[INFO] |  |  |  |  \- org.agrona:agrona:jar:0.9.31:compile
[INFO] |  |  |  \- io.aeron:aeron-client:jar:1.15.1:compile
[INFO] |  |  +- io.jaegertracing:jaeger-client:jar:1.0.0:compile
[INFO] |  |  |  +- io.jaegertracing:jaeger-thrift:jar:1.0.0:compile
[INFO] |  |  |  |  +- org.apache.thrift:libthrift:jar:0.12.0:compile
[INFO] |  |  |  |  \- com.squareup.okhttp3:okhttp:jar:4.9.3:compile
[INFO] |  |  |  |     +- com.squareup.okio:okio:jar:2.8.0:compile
[INFO] |  |  |  |     |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.6.21:compile
[INFO] |  |  |  |     \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.21:compile
[INFO] |  |  |  |        \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  |  |  +- io.jaegertracing:jaeger-core:jar:1.0.0:compile
[INFO] |  |  |  |  +- io.opentracing:opentracing-api:jar:0.33.0:compile
[INFO] |  |  |  |  \- io.opentracing:opentracing-util:jar:0.33.0:compile
[INFO] |  |  |  |     \- io.opentracing:opentracing-noop:jar:0.33.0:compile
[INFO] |  |  |  \- io.jaegertracing:jaeger-tracerresolver:jar:1.0.0:compile
[INFO] |  |  |     \- io.opentracing.contrib:opentracing-tracerresolver:jar:0.1.8:compile
[INFO] |  |  \- com.typesafe:config:jar:1.3.0:compile
[INFO] |  +- com.ppb.platform.stream:stream-protocol-common:jar:3.1.3_2.1_2.11:compile
[INFO] |  |  +- com.ppb.platform.stream:stream-protocol-message:jar:3.1.3_2.1_2.11:compile
[INFO] |  |  +- io.kamon:kamon-core_2.11:jar:1.1.5:compile
[INFO] |  |  |  \- com.lihaoyi:fansi_2.11:jar:0.2.4:compile
[INFO] |  |  |     \- com.lihaoyi:sourcecode_2.11:jar:0.1.3:compile
[INFO] |  |  +- io.kamon:kamon-akka-remote-2.5_2.11:jar:1.1.0:compile
[INFO] |  |  |  \- io.kamon:kamon-scala-future_2.11:jar:1.0.0:compile
[INFO] |  |  +- io.kamon:kamon-akka-2.5_2.11:jar:1.1.3:compile
[INFO] |  |  |  \- io.kamon:kamon-executors_2.11:jar:1.0.1:compile
[INFO] |  |  +- io.kamon:kamon-system-metrics_2.11:jar:1.0.1:compile
[INFO] |  |  +- com.ppb.platform.sb:kamon-jmx-reporter_2.11:jar:2.1:compile
[INFO] |  |  +- org.aspectj:aspectjweaver:jar:1.9.7:compile
[INFO] |  |  +- io.kamon:sigar-loader:jar:1.6.6-rev002:compile
[INFO] |  |  +- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO] |  |  \- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO] |  +- com.ppb.platform.sb:market-stream-client-common:jar:1.48.0:compile
[INFO] |  |  +- com.betfair.monitoring:monitor:jar:2.8.3:compile
[INFO] |  |  |  \- cglib:cglib-nodep:jar:2.1_3:compile
[INFO] |  |  +- com.betfair.platform:cougar-api:jar:4.12.8:compile
[INFO] |  |  |  +- com.betfair.platform:virtualheap:jar:1.2:compile
[INFO] |  |  |  \- com.paddypowerbetfair.libraries:business-context-propagation:jar:1.0.0:compile
[INFO] |  |  +- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  |  \- com.ppb.platform.sb:ppb-sb-executors:jar:1.7:compile
[INFO] |  |     \- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] |  +- com.ppb.platform.sb:market-stream-metrics:jar:1.48.0:compile
[INFO] |  |  \- com.betfair.sportsbook:sportsbook-domain:jar:1.2.27:compile
[INFO] |  |     \- com.ppb.platform.sb:ppb-sportsbook-domain:jar:2.05:compile
[INFO] |  +- com.ppb.platform.sb:market-stream-metrics-kamon:jar:1.48.0:compile
[INFO] |  +- com.ppb.platform.sb:market-stream-model:jar:1.48.0:compile
[INFO] |  |  +- org.apache.commons:commons-collections4:jar:4.1:compile
[INFO] |  |  \- joda-time:joda-time:jar:2.3:compile
[INFO] |  +- com.ppb.platform.sb:market-stream-serializers:jar:1.48.0:compile
[INFO] |  |  +- com.ppb.platform.sb:market-stream-message-definition:jar:1.48.0:compile
[INFO] |  |  +- com.ppb.platform.sb:journal-serialization:jar:1.21:compile
[INFO] |  |  \- com.ppb.platform.sb:pricing-utils:jar:3.9:compile
[INFO] |  |     \- com.ppb.platform.sb:market-model:jar:3.9:compile
[INFO] |  +- com.google.protobuf:protobuf-java:jar:3.24.4:compile
[INFO] |  +- com.betfair.platform:cougar-util:jar:4.12.9:compile
[INFO] |  |  +- org.slf4j:jcl-over-slf4j:jar:1.7.36:compile
[INFO] |  |  +- org.slf4j:jul-to-slf4j:jar:1.7.36:compile
[INFO] |  |  +- org.springframework:spring-aspects:jar:5.3.31:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:5.3.31:compile
[INFO] |  |  +- org.springframework:spring-context:jar:5.3.31:compile
[INFO] |  |  |  \- org.springframework:spring-expression:jar:5.3.31:compile
[INFO] |  |  +- com.betfair.libraries:reloadable-configs:jar:0.4:compile
[INFO] |  |  +- com.maxmind.geoip2:geoip2:jar:2.16.1:compile
[INFO] |  |  |  +- com.maxmind.db:maxmind-db:jar:2.0.0:compile
[INFO] |  |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.13.5:compile
[INFO] |  |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.13.5:compile
[INFO] |  |  +- javax.servlet:javax.servlet-api:jar:4.0.1:compile
[INFO] |  |  +- com.sun.jdmk:jmxtools:jar:1.2.1:compile
[INFO] |  |  +- org.jasypt:jasypt:jar:1.9.0:compile
[INFO] |  |  \- org.jasypt:jasypt-spring2:jar:1.9.0:compile
[INFO] |  +- org.springframework:spring-core:jar:5.3.31:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:5.3.31:compile
[INFO] |  +- org.springframework:spring-beans:jar:5.3.31:compile
[INFO] |  +- com.google.guava:guava:jar:19.0:compile
[INFO] |  +- com.ppb.platform.sb:ppb-sb-feature-toggles-core:jar:1.1:compile
[INFO] |  |  \- com.google.code.findbugs:jsr305:jar:2.0.1:compile
[INFO] |  +- org.scala-lang.modules:scala-java8-compat_2.11:jar:0.7.0:compile
[INFO] |  +- com.ppb.platform.sb:ppb-sb-metrics-core:jar:1.7:compile
[INFO] |  +- com.ppb.platform.sb:ppb-sb-metrics-mantis:jar:1.7:compile
[INFO] |  |  +- com.betfair.mantis.performance:mantis-performance:jar:1.14.6:compile
[INFO] |  |  |  +- com.betfair.sre:statse-client:jar:1.3.0:compile
[INFO] |  |  |  |  \- org.zeromq:jeromq:jar:0.3.3:compile
[INFO] |  |  |  +- org.codehaus.jackson:jackson-core-asl:jar:1.8.1:compile
[INFO] |  |  |  +- com.googlecode.disruptor:disruptor:jar:2.8:compile
[INFO] |  |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.16:compile
[INFO] |  |  |  \- org.apache.commons:commons-math:jar:2.2:compile
[INFO] |  |  +- com.betfair.platform:cougar-core-api:jar:4.10.3:compile
[INFO] |  |  |  \- javax.ws.rs:jsr311-api:jar:1.1:compile
[INFO] |  |  \- com.betfair.platform.sb:cougar-commons:jar:2.40:compile
[INFO] |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  +- org.objenesis:objenesis:jar:3.4:compile
[INFO] |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  +- de.javakaffee:kryo-serializers:jar:0.45:compile
[INFO] |  \- org.slf4j:slf4j-api:jar:1.7.36:compile
[INFO] +- org.springframework.boot:spring-boot-starter-quartz:jar:2.7.18:compile
[INFO] |  +- org.springframework:spring-context-support:jar:5.3.31:compile
[INFO] |  \- org.springframework:spring-tx:jar:5.3.31:compile
[INFO] +- org.quartz-scheduler:quartz:jar:2.3.2:compile
[INFO] |  \- com.mchange:mchange-commons-java:jar:0.2.15:compile
[INFO] +- com.ppb.platform.sb:ppb-sb-metrics-cougar:jar:1.7:compile
[INFO] |  \- com.betfair.monitoring:kpi-quartzpublisher:jar:2.8.3:compile
[INFO] |     +- com.betfair.monitoring:kpi-base-spring:jar:2.8.3:compile
[INFO] |     |  \- com.betfair.monitoring:kpi-base:jar:2.8.3:compile
[INFO] |     |     \- com.jamonapi:jamon:jar:2.4:compile
[INFO] |     \- opensymphony:quartz:jar:1.5.2:compile
[INFO] +- org.springframework.boot:spring-boot-starter:jar:2.7.18:compile
[INFO] |  +- org.springframework.boot:spring-boot:jar:2.7.18:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:2.7.18:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-logging:jar:2.7.18:compile
[INFO] |  |  +- ch.qos.logback:logback-classic:jar:1.2.12:compile
[INFO] |  |  |  \- ch.qos.logback:logback-core:jar:1.2.12:compile
[INFO] |  |  \- org.apache.logging.log4j:log4j-to-slf4j:jar:2.17.2:compile
[INFO] |  |     \- org.apache.logging.log4j:log4j-api:jar:2.17.2:compile
[INFO] |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  \- org.yaml:snakeyaml:jar:1.30:compile
[INFO] +- org.springframework.boot:spring-boot-starter-test:jar:2.7.18:test
[INFO] |  +- org.springframework.boot:spring-boot-test:jar:2.7.18:test
[INFO] |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:2.7.18:test
[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.7.0:test
[INFO] |  |  \- net.minidev:json-smart:jar:2.4.11:test
[INFO] |  |     \- net.minidev:accessors-smart:jar:2.4.11:test
[INFO] |  |        \- org.ow2.asm:asm:jar:9.3:test
[INFO] |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:test
[INFO] |  |  \- jakarta.activation:jakarta.activation-api:jar:1.2.2:test
[INFO] |  +- org.assertj:assertj-core:jar:3.22.0:test
[INFO] |  +- org.hamcrest:hamcrest:jar:2.2:test
[INFO] |  +- org.junit.jupiter:junit-jupiter:jar:5.8.2:test
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-api:jar:5.8.2:test
[INFO] |  |  |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  |  |  +- org.junit.platform:junit-platform-commons:jar:1.8.2:test
[INFO] |  |  |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-params:jar:5.8.2:test
[INFO] |  |  \- org.junit.jupiter:junit-jupiter-engine:jar:5.8.2:test
[INFO] |  |     \- org.junit.platform:junit-platform-engine:jar:1.8.2:test
[INFO] |  +- org.mockito:mockito-core:jar:4.5.1:test
[INFO] |  |  +- net.bytebuddy:byte-buddy:jar:1.12.23:test
[INFO] |  |  \- net.bytebuddy:byte-buddy-agent:jar:1.12.23:test
[INFO] |  +- org.mockito:mockito-junit-jupiter:jar:4.5.1:test
[INFO] |  +- org.skyscreamer:jsonassert:jar:1.5.1:test
[INFO] |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:test
[INFO] |  +- org.springframework:spring-test:jar:5.3.31:test
[INFO] |  \- org.xmlunit:xmlunit-core:jar:2.9.1:test
[INFO] +- io.hawt:hawtio-springboot:jar:3.0.2:compile
[INFO] |  +- io.hawt:hawtio-system:jar:3.0.2:compile
[INFO] |  |  +- org.jolokia:jolokia-core:jar:1.7.2:compile
[INFO] |  |  |  \- com.googlecode.json-simple:json-simple:jar:1.1.1:compile
[INFO] |  |  +- org.apache.httpcomponents:httpclient:jar:4.5.14:compile
[INFO] |  |  +- commons-io:commons-io:jar:2.16.1:compile
[INFO] |  |  +- commons-codec:commons-codec:jar:1.15:compile
[INFO] |  |  \- org.json:json:jar:20240303:compile
[INFO] |  +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:2.7.18:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.13.5:compile
[INFO] |  |  \- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.13.5:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-web:jar:2.7.18:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-json:jar:2.7.18:compile
[INFO] |     |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.13.5:compile
[INFO] |     |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.13.5:compile
[INFO] |     +- org.springframework:spring-web:jar:5.3.31:compile
[INFO] |     \- org.springframework:spring-webmvc:jar:5.3.31:compile
[INFO] +- org.springframework.boot:spring-boot-starter-tomcat:jar:2.7.18:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.83:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.83:compile
[INFO] |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.83:compile
[INFO] +- org.springframework.boot:spring-boot-actuator:jar:2.7.18:compile
[INFO] \- org.testng:testng:jar:6.9.10:compile
[INFO]    +- com.beust:jcommander:jar:1.48:compile
[INFO]    \- org.beanshell:bsh:jar:2.0b4:compile
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  5.424 s
[INFO] Finished at: 2025-09-18T16:28:03+03:00
[INFO] ------------------------------------------------------------------------
