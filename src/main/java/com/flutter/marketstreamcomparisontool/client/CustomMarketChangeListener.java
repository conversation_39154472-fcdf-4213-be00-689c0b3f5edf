package com.flutter.marketstreamcomparisontool.client;

import com.betfair.platform.fms.MarketChangeListener;
import com.betfair.platform.fms.model.MarketChanges;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class CustomMarketChangeListener implements MarketChangeListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomMarketChangeListener.class);
    @Override
    public void onMarketChanges(int i, long l, MarketChanges marketChanges) {
        LOGGER.info("onMarketChanges: {}", marketChanges);
    }
}
