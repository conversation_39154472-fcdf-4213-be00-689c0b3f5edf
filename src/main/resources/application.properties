# GSSP Client/Consumer/Container config locations
market.stream.client.config.gssp=conf/market-stream-client-gssp.properties
market.stream.consumer.config.gssp=conf/consumer-gssp.conf
market.stream.consumer.container.config.gssp=conf/consumer-container-gssp.conf

# FMG Client/Consumer/Container config locations
market.stream.client.config.fmg=conf/market-stream-client-fmg.properties
market.stream.consumer.config.fmg=conf/consumer-fmg.conf
market.stream.consumer.container.config.fmg=conf/consumer-container-fmg.conf

# Feature toggles
market.stream.client.enabled.gssp=true
market.stream.client.enabled.fmg=true

spring.jmx.enabled=true

management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.include=*
management.endpoint.health.show-details=always


hawtio.authenticationEnabled=false